#!/bin/bash

# Jenkins Deployment Script for Frontend
# This script handles Git conflicts and deploys the application

set -e  # Exit on any error

PROJECT_NAME="ebadollar"
DEPLOY_PATH="/var/www/${PROJECT_NAME}.manaknightdigital.com"
NGINX_CONFIG="/etc/nginx/sites-available/${PROJECT_NAME}.manaknightdigital.com"

echo "🔄 Starting Jenkins deployment process..."

# Navigate to project directory
cd ${PROJECT_NAME}_frontend/

echo "📦 Switching to master branch..."
sudo git switch master

echo "💾 Handling local changes..."
# Add all changes to staging
sudo git add .

# Stash any local changes to avoid conflicts
sudo git stash push -m "Jenkins deployment stash $(date)" || echo "No changes to stash"

echo "🧹 Cleaning workspace..."
# Reset to ensure clean state
sudo git reset --hard HEAD
sudo git clean -fd

echo "⬇️ Pulling latest changes..."
# Pull latest changes from remote
sudo git pull origin master

echo "📦 Building application..."
# Install dependencies and build
sudo npm install --force
sudo npm run build

echo "📁 Setting up deployment directory..."
# Ensure deploy directory exists
sudo mkdir -p $DEPLOY_PATH

echo "📋 Copying files to deployment directory..."
# Copy build files to deployment directory
sudo cp -r dist/* $DEPLOY_PATH/

echo "🔒 Setting permissions..."
# Set proper permissions
sudo chown -R www-data:www-data $DEPLOY_PATH
sudo chmod -R 755 $DEPLOY_PATH

echo "🔍 Testing Nginx configuration..."
# Test Nginx configuration
sudo nginx -t

echo "🔄 Reloading Nginx..."
# Reload Nginx to apply changes
sudo systemctl reload nginx

# Navigate back
cd ../

echo "✅ Deployment completed successfully!"
echo "🌐 Application is now live at: https://${PROJECT_NAME}.manaknightdigital.com"
